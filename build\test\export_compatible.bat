@echo off
echo ========================================
echo  Exporting Godot Project for Web
echo  (Compatible Mode - No Threading)
echo ========================================
echo.
echo This will export your project with:
echo - Threading disabled for better compatibility
echo - Cross-Origin isolation headers disabled
echo - Works on more devices and browsers
echo.
echo Make sure Godot is closed before running this.
echo.
pause

cd /d "%~dp0..\.."

echo Exporting project...
echo.

REM Export the project using Godot command line (RELEASE mode for web compatibility)
"D:\Steam\steamapps\common\Godot Engine\godot.windows.opt.tools.64.exe" --headless --export-release "Web" "build/test/stiletto.html"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo  Export completed successfully!
    echo ========================================
    echo.
    echo Files exported to: build/test/
    echo.
    echo You can now test with:
    echo   python build/test/test_server.py
    echo.
    echo Or use the remote server:
    echo   build/test/start_remote_server.bat
    echo.
) else (
    echo.
    echo ========================================
    echo  Export failed!
    echo ========================================
    echo.
    echo Make sure:
    echo 1. Godot is installed at: D:\Steam\steamapps\common\Godot Engine\
    echo 2. The project is not open in Godot editor
    echo 3. You have the Web export template installed
    echo.
)

pause
