<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">
    <link id='-gd-engine-icon' rel='icon' type='image/png' href='favicon.png'>
    <title><PERSON><PERSON><PERSON> Proto</title>

    <style>
        :root {
            --bg-color: #2a2a2a;
            --panel-bg: #3a3a3a;
            --button-bg: #4a4a4a;
            --button-hover: #5a5a5a;
            --text-color: #e0e0e0;
            --border-color: #555;
            --game-width: min(90vw, 1200px);
            --game-height: min(70vh, 900px);
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            border: 0;
            outline: 0;
        }

        body {
            touch-action: none;
            text-align: center;
            background-color: var(--bg-color);
            font-family: 'Arial', sans-serif;
            color: var(--text-color);
            overflow: hidden;
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px 20px 60px 20px; /* Minimal padding, more space for bottom controls */
            box-sizing: border-box;
        }

        /* Decorative graphics positioned directly adjacent to viewport edges */
        .decorative-graphics {
            position: absolute;
            pointer-events: none; /* Don't interfere with game interaction */
            z-index: 500; /* Below controls but above background */
            opacity: 0.8; /* Subtle transparency */
            transition: opacity 0.3s ease; /* Smooth fade effects */
            /* Pixel perfect rendering - no scaling */
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
        }

        /* Hover effect on game container makes decorations more visible */
        .game-container:hover .decorative-graphics {
            opacity: 1.0;
        }

        /* Calculate viewport position and place decorations around it */
        /* These will be positioned relative to the game-container */

        /* Left side decoration - positioned to the left of viewport */
        .decorative-left-edge {
            top: 50%;
            right: calc(50% + var(--game-width)/2 + 10px); /* Left of viewport with gap */
            transform: translateY(-50%);
        }

        /* Right side decoration - positioned to the right of viewport */
        .decorative-right-edge {
            top: 50%;
            left: calc(50% + var(--game-width)/2 + 10px); /* Right of viewport with gap */
            transform: translateY(-50%);
        }

        /* Top decoration - positioned above viewport */
        .decorative-top-edge {
            bottom: calc(50% + var(--game-height)/2 + 10px); /* Above viewport with gap */
            left: 50%;
            transform: translateX(-50%);
        }

        /* Bottom decoration - positioned below viewport */
        .decorative-bottom-edge {
            top: calc(50% + var(--game-height)/2 + 10px); /* Below viewport with gap */
            left: 50%;
            transform: translateX(-50%);
        }

        /* Corner decorations */
        .decorative-top-left {
            bottom: calc(50% + var(--game-height)/2 + 10px);
            right: calc(50% + var(--game-width)/2 + 10px);
        }

        .decorative-top-right {
            bottom: calc(50% + var(--game-height)/2 + 10px);
            left: calc(50% + var(--game-width)/2 + 10px);
        }

        .decorative-bottom-left {
            top: calc(50% + var(--game-height)/2 + 10px);
            right: calc(50% + var(--game-width)/2 + 10px);
        }

        .decorative-bottom-right {
            top: calc(50% + var(--game-height)/2 + 10px);
            left: calc(50% + var(--game-width)/2 + 10px);
        }

        .game-viewport {
            position: relative;
            width: var(--game-width);
            height: var(--game-height);
            max-width: 100vw;
            max-height: 100vh;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #canvas {
            display: block;
            margin: 0;
            padding: 0;
            color: white;
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            background: transparent;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        #canvas:focus {
            outline: none;
        }

        /* Fullscreen mode styles - eliminate ALL spacing and ensure viewport fills entire screen */
        :fullscreen,
        :-webkit-full-screen,
        :-moz-full-screen,
        :-ms-fullscreen {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: #000 !important;
        }

        :fullscreen *,
        :-webkit-full-screen *,
        :-moz-full-screen *,
        :-ms-fullscreen * {
            box-sizing: border-box !important;
        }

        :fullscreen html,
        :-webkit-full-screen html,
        :-moz-full-screen html,
        :-ms-fullscreen html {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            overflow: hidden !important;
            background: #000 !important;
        }

        :fullscreen body,
        :-webkit-full-screen body,
        :-moz-full-screen body,
        :-ms-fullscreen body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            overflow: hidden !important;
            background: #000 !important;
            display: block !important;
        }

        :fullscreen .game-container,
        :-webkit-full-screen .game-container,
        :-moz-full-screen .game-container,
        :-ms-fullscreen .game-container {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            z-index: 9999 !important;
            background: #000 !important;
            display: block !important;
            flex-direction: unset !important;
            align-items: unset !important;
            justify-content: unset !important;
        }

        :fullscreen .game-viewport,
        :-webkit-full-screen .game-viewport,
        :-moz-full-screen .game-viewport,
        :-ms-fullscreen .game-viewport {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            max-height: none !important;
            max-width: none !important;
            border: none !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            z-index: 10000 !important;
            background: #000 !important;
        }

        :fullscreen .control-panel,
        :-webkit-full-screen .control-panel,
        :-moz-full-screen .control-panel,
        :-ms-fullscreen .control-panel {
            display: none !important;
        }

        :fullscreen .viewport-controls-container,
        :-webkit-full-screen .viewport-controls-container,
        :-moz-full-screen .viewport-controls-container,
        :-ms-fullscreen .viewport-controls-container,
        :fullscreen .viewport-logo-side,
        :-webkit-full-screen .viewport-logo-side,
        :-moz-full-screen .viewport-logo-side,
        :-ms-fullscreen .viewport-logo-side,
        :fullscreen .viewport-controls-side,
        :-webkit-full-screen .viewport-controls-side,
        :-moz-full-screen .viewport-controls-side,
        :-ms-fullscreen .viewport-controls-side,
        :fullscreen .decorative-graphics,
        :-webkit-full-screen .decorative-graphics,
        :-moz-full-screen .decorative-graphics,
        :-ms-fullscreen .decorative-graphics {
            display: none !important;
        }

        /* Ensure canvas fills fullscreen properly */
        :fullscreen #canvas,
        :-webkit-full-screen #canvas,
        :-moz-full-screen #canvas,
        :-ms-fullscreen #canvas {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            border-radius: 0 !important;
            display: block !important;
            background: transparent !important;
            z-index: 1 !important;
        }



        /* Controls positioned below viewport */
        .viewport-controls-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: var(--game-width);
            margin-top: 10px;
            z-index: 1000;
        }

        .viewport-logo {
            height: 32px;
            width: auto;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .viewport-logo:hover {
            opacity: 1.0;
        }

        .viewport-controls {
            display: flex;
            gap: 8px;
        }

        .viewport-control-button {
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            border: none;
            border-radius: 0;
            padding: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
        }

        .viewport-control-button:hover {
            color: rgba(255, 255, 255, 1);
            background: rgba(255, 255, 255, 0.1);
        }

        .viewport-control-button:active {
            color: rgba(255, 255, 255, 0.9);
        }

        /* External controls for responsive layout */
        .viewport-logo-side {
            position: fixed;
            left: calc((100vw - var(--game-width)) / 2 - 50px);
            top: 50%;
            transform: translateY(-50%);
            height: 28px;
            width: auto;
            opacity: 0.8;
            z-index: 1001;
            transition: all 0.3s ease;
            display: none;
        }

        .viewport-logo-side:hover {
            opacity: 1.0;
        }

        .viewport-controls-side {
            position: fixed;
            right: calc((100vw - var(--game-width)) / 2 - 50px);
            top: 50%;
            transform: translateY(-50%);
            display: none;
            flex-direction: column;
            gap: 8px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        /* Responsive layout: Flexible viewport like desktop Godot */
        @media (max-height: 800px), (max-width: 1000px) {
            :root {
                --game-width: 100vw;
                --game-height: 100vh;
            }

            .game-container {
                padding: 0;
                margin: 0;
                justify-content: center;
                align-items: center;
                width: 100vw;
                height: 100vh;
            }

            .game-viewport {
                /* Use available space like desktop Godot - no forced aspect ratio */
                width: var(--game-width);
                height: var(--game-height);
                max-height: 100vh;
                max-width: 100vw;
                border: 1px solid var(--border-color);
                border-radius: 0;
                margin: 0;
                padding: 0;
                box-sizing: border-box; /* Include border in dimensions */
            }

            /* Hide bottom controls */
            .viewport-controls-container {
                display: none;
            }

            /* Show external controls as overlays */
            .viewport-logo-side {
                display: block;
                left: 15px; /* Fixed position from left edge */
            }

            .viewport-controls-side {
                display: flex;
                right: 15px; /* Fixed position from right edge */
            }

            /* Hide decorative graphics on smaller screens to avoid interference */
            .decorative-graphics {
                display: none;
            }
        }

        /* Extra compact for very small screens - overlay directly on viewport */
        @media (max-height: 600px) {
            :root {
                --game-width: 100vw;
                --game-height: 100vh;
            }

            .game-container {
                padding: 0; /* No padding */
            }

            .game-viewport {
                height: 100vh; /* Full screen height */
                width: 100vw; /* Full screen width */
                border: none;
                border-radius: 0;
            }

            .viewport-logo-side {
                left: 10px; /* Closer to edge */
                height: 24px;
            }

            .viewport-controls-side {
                right: 10px; /* Closer to edge */
                gap: 6px;
            }

            .viewport-controls-side .viewport-control-button {
                width: 28px;
                height: 28px;
                padding: 4px;
            }
        }

        /* Legacy control panel (keep for compatibility) */
        .control-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: none; /* Hidden by default, header controls are primary */
            gap: 10px;
            z-index: 1000;
        }

        .control-button {
            background: var(--button-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .control-button:hover {
            background: var(--button-hover);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .control-button:active {
            transform: translateY(0);
        }

        .settings-panel {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--panel-bg);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            min-width: 700px;
            max-width: 900px;
            z-index: 2000;
            display: none;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
        }

        .settings-panel.show {
            display: block;
        }

        .settings-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
            color: var(--text-color);
        }

        .setting-input {
            width: 100%;
            padding: 8px;
            background: var(--button-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 4px;
        }

        .setting-input:focus {
            outline: none;
            border-color: #007acc;
        }

        .resolution-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-top: 8px;
        }

        .resolution-option {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--button-bg);
            cursor: pointer;
            transition: all 0.2s;
        }

        .resolution-option:hover {
            background: var(--button-hover);
            border-color: #007acc;
        }

        .resolution-option.selected {
            background: #007acc;
            border-color: #0099ff;
        }

        .resolution-radio {
            margin-right: 10px;
            accent-color: #007acc;
        }

        .resolution-label {
            flex: 1;
            color: var(--text-color);
            font-size: 14px;
        }

        .resolution-dimensions {
            color: #999;
            font-size: 12px;
            margin-left: 10px;
        }

        /* Horizontal button groups */
        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 8px;
        }

        .option-button {
            flex: 1;
            min-width: 100px;
            padding: 10px 12px;
            background: var(--button-bg);
            border: 2px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            font-size: 13px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .option-button:hover {
            background: var(--button-hover-bg);
            border-color: var(--button-hover-border);
        }

        .option-button.selected {
            background: var(--button-active-bg);
            border-color: var(--button-active-border);
            color: var(--button-active-text);
        }

        .option-button .button-label {
            font-weight: bold;
        }

        .option-button .button-description {
            font-size: 11px;
            opacity: 0.8;
        }

        /* Aspect ratio specific styling */
        .aspect-ratio-group {
            display: none;
            margin-top: 10px;
        }

        .aspect-ratio-group.show {
            display: block;
        }

        .aspect-ratio-group .button-group {
            gap: 6px;
        }

        .aspect-ratio-group .option-button {
            min-width: 80px;
            padding: 8px 10px;
            font-size: 12px;
        }

        .settings-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            display: none;
        }

        .overlay.show {
            display: block;
        }



        /* Loading screen contained within viewport */
        #loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: #1a1a1a;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        #loading-screen.visible {
            visibility: visible;
            opacity: 1;
        }

        .loading-logo {
            width: 48px;
            height: 48px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }

        .custom-progress-container {
            width: 200px;
            height: 4px;
            background: #333;
            overflow: hidden;
            position: relative;
        }

        .custom-progress-bar {
            height: 100%;
            background: #666;
            width: 0%;
            transition: width 0.2s ease;
        }

        .custom-progress-indeterminate {
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                transparent 30%,
                #666 50%,
                transparent 70%,
                transparent 100%);
            background-size: 50px 100%;
            animation: indeterminateSlide 1.5s linear infinite;
            display: none;
        }

        @keyframes indeterminateSlide {
            0% { background-position: -50px 0; }
            100% { background-position: 250px 0; }
        }

        .loading-data-counter {
            color: #666;
            font-size: 11px;
            font-family: monospace;
            margin-top: 8px;
            text-align: center;
            min-height: 13px;
        }

        /* Hide old status elements */
        #status {
            display: none !important;
        }

        .godot {
            font-family: 'Noto Sans', 'Droid Sans', Arial, sans-serif;
            color: #e0e0e0;
            background-color: var(--bg-color);
        }

        img {
            width: 100%;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        /* Responsive design */
        @media (max-width: 900px) {
            :root {
                --game-width: min(95vw, 800px);
                --game-height: min(75vh, 600px);
            }

            .game-container {
                padding: 5px;
            }

            .control-panel {
                bottom: 10px;
                right: 10px;
                gap: 5px;
            }

            .control-button {
                padding: 6px 12px;
                font-size: 11px;
                min-width: 60px;
            }
        }
    </style>

    <!-- Material progress bar -->
    <style>.progress{position:relative;height:4px;display:block;width:100%;background-color:#acece6;border-radius:2px;background-clip:padding-box;margin:0.5rem 0 1rem;overflow:hidden}.progress .determinate{position:absolute;background-color:inherit;top:0;bottom:0;background-color:#26a69a;transition:width 0.3s linear}.progress .indeterminate{background-color:#26a69a}.progress .indeterminate:before{content:'';position:absolute;background-color:inherit;top:0;left:0;bottom:0;will-change:left, right;-webkit-animation:indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;animation:indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite}.progress .indeterminate:after{content:'';position:absolute;background-color:inherit;top:0;left:0;bottom:0;will-change:left, right;-webkit-animation:indeterminate-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;animation:indeterminate-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;-webkit-animation-delay:1.15s;animation-delay:1.15s}@-webkit-keyframes indeterminate{0%{left:-35%;right:100%}60%{left:100%;right:-90%}100%{left:100%;right:-90%}}@keyframes indeterminate{0%{left:-35%;right:100%}60%{left:100%;right:-90%}100%{left:100%;right:-90%}}@-webkit-keyframes indeterminate-short{0%{left:-200%;right:100%}60%{left:107%;right:-8%}100%{left:107%;right:-8%}}@keyframes indeterminate-short{0%{left:-200%;right:100%}60%{left:107%;right:-8%}100%{left:107%;right:-8%}}</style>

<!-- Godot head includes would go here -->
</head>
<body>
    <div class="game-container">
        <!-- Decorative graphics positioned around viewport -->
        <img src="decorative/top-left.png" class="decorative-graphics decorative-top-left" alt="">
        <img src="decorative/top-right.png" class="decorative-graphics decorative-top-right" alt="">
        <img src="decorative/bottom-left.png" class="decorative-graphics decorative-bottom-left" alt="">
        <img src="decorative/bottom-right.png" class="decorative-graphics decorative-bottom-right" alt="">
        <img src="decorative/left-edge.png" class="decorative-graphics decorative-left-edge" alt="">
        <img src="decorative/right-edge.png" class="decorative-graphics decorative-right-edge" alt="">
        <img src="decorative/top-edge.png" class="decorative-graphics decorative-top-edge" alt="">
        <img src="decorative/bottom-edge.png" class="decorative-graphics decorative-bottom-edge" alt="">

        <div class="game-viewport">
            <canvas id='canvas'>
                HTML5 canvas appears to be unsupported in the current browser.<br />
                Please try updating or use a different browser.
            </canvas>

            <!-- Custom loading screen contained within viewport -->
            <div id="loading-screen">
                <div class="loading-logo">
                    <img src="stiletto.png" alt="Logo">
                </div>
                <div class="custom-progress-container">
                    <div class="custom-progress-bar" id="custom-progress-bar"></div>
                    <div class="custom-progress-indeterminate" id="custom-progress-indeterminate"></div>
                </div>
                <div class="loading-data-counter" id="loading-data-counter"></div>
            </div>

            <!-- Side-positioned controls for responsive layout -->
            <img src="stiletto.png" alt="Stiletto" class="viewport-logo-side">
            <div class="viewport-controls-side">
                <button class="viewport-control-button" id="viewport-settings-btn-side" title="Settings">
                    <!-- Settings cog icon -->
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 8.5m-7 7l-2.5 2.5M4.5 4.5L7 7m7 7l2.5 2.5M4.5 19.5L7 17"></path>
                    </svg>
                </button>
                <button class="viewport-control-button" id="viewport-fullscreen-btn-side" title="Fullscreen">
                    <!-- Fullscreen expand icon -->
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
                    </svg>
                </button>
            </div>

        </div>

        <!-- Controls positioned below viewport -->
        <div class="viewport-controls-container">
            <img src="stiletto.png" alt="Stiletto" class="viewport-logo">
            <div class="viewport-controls">
                <button class="viewport-control-button" id="viewport-settings-btn" title="Settings">
                    <!-- Settings cog icon -->
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 8.5m-7 7l-2.5 2.5M4.5 4.5L7 7m7 7l2.5 2.5M4.5 19.5L7 17"></path>
                    </svg>
                </button>
                <button class="viewport-control-button" id="viewport-fullscreen-btn" title="Fullscreen">
                    <!-- Fullscreen expand icon -->
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
                    </svg>
                </button>
            </div>
        </div>



        <!-- Legacy control panel (hidden by default) -->
        <div class="control-panel">
            <button class="control-button" id="fullscreen-btn">Fullscreen</button>
            <button class="control-button" id="settings-btn">Settings</button>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>

    <div class="settings-panel" id="settings-panel">
        <div class="settings-header">Game Settings</div>



        <div class="setting-group">
            <label class="setting-label">Physical Resolution</label>
            <div class="button-group">
                <div class="option-button" data-width="640" data-height="480" data-value="640x480">
                    <input type="radio" name="physical-resolution" value="640x480" id="physical-480p" style="display: none;">
                    <span class="button-label">480p</span>
                    <span class="button-description">640×480</span>
                </div>
                <div class="option-button selected" data-width="800" data-height="600" data-value="800x600">
                    <input type="radio" name="physical-resolution" value="800x600" id="physical-svga" style="display: none;" checked>
                    <span class="button-label">SVGA</span>
                    <span class="button-description">800×600</span>
                </div>
                <div class="option-button" data-width="1024" data-height="768" data-value="1024x768">
                    <input type="radio" name="physical-resolution" value="1024x768" id="physical-xga" style="display: none;">
                    <span class="button-label">XGA</span>
                    <span class="button-description">1024×768</span>
                </div>
                <div class="option-button" data-width="1280" data-height="720" data-value="1280x720">
                    <input type="radio" name="physical-resolution" value="1280x720" id="physical-720p" style="display: none;">
                    <span class="button-label">720p</span>
                    <span class="button-description">1280×720</span>
                </div>
                <div class="option-button" data-width="1920" data-height="1080" data-value="1920x1080">
                    <input type="radio" name="physical-resolution" value="1920x1080" id="physical-1080p" style="display: none;">
                    <span class="button-label">1080p</span>
                    <span class="button-description">1920×1080</span>
                </div>
            </div>
        </div>

        <div class="setting-group">
            <label class="setting-label">Viewport Scaling</label>
            <div class="button-group">
                <div class="option-button selected" data-scaling="fixed" data-value="fixed">
                    <input type="radio" name="viewport-scaling" value="fixed" id="scaling-fixed" style="display: none;" checked>
                    <span class="button-label">Fixed Resolution</span>
                    <span class="button-description">Set dimensions</span>
                </div>
                <div class="option-button" data-scaling="dynamic" data-value="dynamic">
                    <input type="radio" name="viewport-scaling" value="dynamic" id="scaling-dynamic" style="display: none;">
                    <span class="button-label">Dynamic Scaling</span>
                    <span class="button-description">Window-based</span>
                </div>
            </div>

            <div class="aspect-ratio-group" id="aspect-ratio-group">
                <label class="setting-label">Aspect Ratio</label>
                <div class="button-group">
                    <div class="option-button selected" data-aspect="4:3" data-value="4:3">
                        <input type="radio" name="aspect-ratio" value="4:3" id="aspect-4-3" style="display: none;" checked>
                        <span class="button-label">4:3</span>
                        <span class="button-description">Classic</span>
                    </div>
                    <div class="option-button" data-aspect="16:10" data-value="16:10">
                        <input type="radio" name="aspect-ratio" value="16:10" id="aspect-16-10" style="display: none;">
                        <span class="button-label">16:10</span>
                        <span class="button-description">Widescreen</span>
                    </div>
                    <div class="option-button" data-aspect="16:9" data-value="16:9">
                        <input type="radio" name="aspect-ratio" value="16:9" id="aspect-16-9" style="display: none;">
                        <span class="button-label">16:9</span>
                        <span class="button-description">HD</span>
                    </div>
                    <div class="option-button" data-aspect="21:9" data-value="21:9">
                        <input type="radio" name="aspect-ratio" value="21:9" id="aspect-21-9" style="display: none;">
                        <span class="button-label">21:9</span>
                        <span class="button-description">Ultrawide</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="setting-group">
            <label class="setting-label" for="bg-color">Background Color</label>
            <input type="color" id="bg-color" class="setting-input" value="#2a2a2a">
        </div>

        <div class="settings-buttons">
            <button class="control-button" id="reset-btn">Reset</button>
            <button class="control-button" id="apply-btn">Apply</button>
            <button class="control-button" id="close-btn">Close</button>
        </div>
    </div>



    <script type='text/javascript' src='stiletto.js'></script>
    <script type='text/javascript'>//<![CDATA[
        // Check if Engine is available
        if (typeof Engine === 'undefined') {
            console.error('Godot Engine class not found! Make sure stiletto.js is loaded correctly.');
            document.body.innerHTML = '<div style="color: red; text-align: center; margin-top: 50px;"><h2>Error: Godot Engine not loaded</h2><p>Please check that stiletto.js is in the same directory and accessible.</p></div>';
            throw new Error('Godot Engine not loaded');
        }

        let engine;
        try {
            engine = new Engine();
            console.log('Engine instance created successfully:', engine);
            console.log('Available engine methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(engine)));
        } catch (error) {
            console.error('Failed to create Engine instance:', error);
            document.body.innerHTML = '<div style="color: red; text-align: center; margin-top: 50px;"><h2>Error: Failed to create Godot Engine</h2><p>Error: ' + error.message + '</p></div>';
            throw error;
        }

        let setStatusMode, setStatusNotice;

        // Game shell functionality
        class GameShell {
            constructor() {
                this.gameViewport = document.querySelector('.game-viewport');
                this.canvas = document.getElementById('canvas');
                this.settingsPanel = document.getElementById('settings-panel');
                this.overlay = document.getElementById('overlay');

                // Viewport controls
                this.viewportFullscreenBtn = document.getElementById('viewport-fullscreen-btn');
                this.viewportSettingsBtn = document.getElementById('viewport-settings-btn');

                // Side viewport controls
                this.viewportFullscreenBtnSide = document.getElementById('viewport-fullscreen-btn-side');
                this.viewportSettingsBtnSide = document.getElementById('viewport-settings-btn-side');

                // Legacy controls (fallback)
                this.fullscreenBtn = document.getElementById('fullscreen-btn');
                this.settingsBtn = document.getElementById('settings-btn');
                this.closeBtn = document.getElementById('close-btn');
                this.applyBtn = document.getElementById('apply-btn');
                this.resetBtn = document.getElementById('reset-btn');

                // Button-based options
                this.optionButtons = document.querySelectorAll('.option-button');
                this.physicalResolutionButtons = document.querySelectorAll('.option-button[data-value*="x"]');
                this.viewportScalingButtons = document.querySelectorAll('.option-button[data-scaling]');
                this.aspectRatioButtons = document.querySelectorAll('.option-button[data-aspect]');
                this.aspectRatioGroup = document.getElementById('aspect-ratio-group');

                // Hidden radio inputs for compatibility
                this.physicalResolutionRadios = document.querySelectorAll('input[name="physical-resolution"]');
                this.viewportScalingRadios = document.querySelectorAll('input[name="viewport-scaling"]');
                this.aspectRatioRadios = document.querySelectorAll('input[name="aspect-ratio"]');

                // Resolution management
                this.physicalResolution = { width: 800, height: 600 };
                this.viewportScaling = 'fixed'; // 'fixed' or 'dynamic'
                this.aspectRatio = '4:3'; // '4:3', '16:10', '16:9', '21:9'
                this.dynamicScaleRatio = 1.0;

                // Fullscreen state management
                this.isFullscreen = false;
                this.savedViewportSettings = {
                    width: 800,
                    height: 600
                };
                this.fullscreenResolution = {
                    width: 1920,
                    height: 1080
                };

                this.initializeEventListeners();
                this.loadSettings();
                this.detectScreenResolution();
            }

            initializeEventListeners() {
                // Viewport controls
                if (this.viewportFullscreenBtn) {
                    this.viewportFullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
                }
                if (this.viewportSettingsBtn) {
                    this.viewportSettingsBtn.addEventListener('click', () => this.openSettings());
                }

                // Side viewport controls
                if (this.viewportFullscreenBtnSide) {
                    this.viewportFullscreenBtnSide.addEventListener('click', () => this.toggleFullscreen());
                }
                if (this.viewportSettingsBtnSide) {
                    this.viewportSettingsBtnSide.addEventListener('click', () => this.openSettings());
                }

                // Legacy controls (fallback)
                if (this.fullscreenBtn) {
                    this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
                }
                if (this.settingsBtn) {
                    this.settingsBtn.addEventListener('click', () => this.openSettings());
                }
                this.closeBtn.addEventListener('click', () => this.closeSettings());
                this.applyBtn.addEventListener('click', () => this.applySettings());
                this.resetBtn.addEventListener('click', () => this.resetSettings());
                this.overlay.addEventListener('click', () => this.closeSettings());

                // Button-based option event listeners
                this.optionButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        // Find the hidden radio input
                        const radio = button.querySelector('input[type="radio"]');
                        if (radio) {
                            // Update radio selection
                            radio.checked = true;

                            // Update visual selection within the same group
                            const groupButtons = button.parentElement.querySelectorAll('.option-button');
                            groupButtons.forEach(btn => btn.classList.remove('selected'));
                            button.classList.add('selected');

                            // Handle specific button types
                            if (button.hasAttribute('data-scaling')) {
                                this.updateViewportScaling();
                            }

                            // Handle resolution changes - update Godot's internal resolution
                            if (radio.name === 'physical-resolution') {
                                console.log(`🎯 Resolution setting changed: ${radio.name} = ${radio.value}`);

                                // For responsive mode, immediately update Godot's resolution
                                if (this.viewportScaling === 'dynamic') {
                                    setTimeout(() => {
                                        if (window.adjustCanvasDimensions) {
                                            console.log('Resolution changed in responsive mode, updating Godot resolution');
                                            window.adjustCanvasDimensions();
                                        }
                                    }, 100);
                                }
                            }

                            this.updateButtonSelections();
                        }
                    });
                });

                // Close settings with Escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.closeSettings();
                    }
                });

                // Window resize for dynamic scaling and responsive mode
                window.addEventListener('resize', () => {
                    if (this.viewportScaling === 'dynamic') {
                        this.updateDynamicScaling();
                    }

                    // In responsive mode, update canvas dimensions to match new viewport size
                    // This mimics desktop Godot behavior where the game adapts to window size
                    setTimeout(() => {
                        if (window.adjustCanvasDimensions) {
                            console.log('Window resized, adjusting canvas dimensions for responsive behavior');
                            window.adjustCanvasDimensions();
                        }
                    }, 50);
                });

                // Handle fullscreen change events (F11, browser controls, etc.)
                document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('webkitfullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('mozfullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('MSFullscreenChange', () => this.handleFullscreenChange());
            }

            detectScreenResolution() {
                // Try to get the actual screen resolution
                if (screen && screen.width && screen.height) {
                    this.fullscreenResolution.width = screen.width;
                    this.fullscreenResolution.height = screen.height;
                    console.log(`Detected screen resolution: ${this.fullscreenResolution.width}x${this.fullscreenResolution.height}`);
                } else {
                    // Fallback to common resolutions based on window size
                    const windowWidth = window.innerWidth;
                    const windowHeight = window.innerHeight;

                    if (windowWidth >= 2560) {
                        this.fullscreenResolution = { width: 2560, height: 1440 };
                    } else if (windowWidth >= 1920) {
                        this.fullscreenResolution = { width: 1920, height: 1080 };
                    } else if (windowWidth >= 1680) {
                        this.fullscreenResolution = { width: 1680, height: 1050 };
                    } else if (windowWidth >= 1440) {
                        this.fullscreenResolution = { width: 1440, height: 900 };
                    } else {
                        this.fullscreenResolution = { width: 1280, height: 720 };
                    }
                    console.log(`Estimated fullscreen resolution: ${this.fullscreenResolution.width}x${this.fullscreenResolution.height}`);
                }
            }

            toggleFullscreen() {
                if (!this.isFullscreen) {
                    this.enterFullscreen();
                } else {
                    this.exitFullscreen();
                }
            }

            async enterFullscreen() {
                try {
                    console.log('Entering fullscreen mode...');

                    // Save current viewport settings from selected resolution
                    const selectedResolution = document.querySelector('input[name="resolution"]:checked');
                    const selectedOption = selectedResolution ? selectedResolution.closest('.resolution-option') : null;

                    if (selectedOption) {
                        this.savedViewportSettings.width = parseInt(selectedOption.dataset.width);
                        this.savedViewportSettings.height = parseInt(selectedOption.dataset.height);
                    } else {
                        this.savedViewportSettings.width = 800;
                        this.savedViewportSettings.height = 600;
                    }
                    console.log(`Saved viewport settings: ${this.savedViewportSettings.width}x${this.savedViewportSettings.height}`);

                    // Request fullscreen on the document element
                    const fullscreenElement = document.documentElement;
                    if (fullscreenElement.requestFullscreen) {
                        await fullscreenElement.requestFullscreen();
                    } else if (fullscreenElement.webkitRequestFullscreen) {
                        await fullscreenElement.webkitRequestFullscreen();
                    } else if (fullscreenElement.mozRequestFullScreen) {
                        await fullscreenElement.mozRequestFullScreen();
                    } else if (fullscreenElement.msRequestFullscreen) {
                        await fullscreenElement.msRequestFullscreen();
                    } else {
                        throw new Error('Fullscreen API not supported');
                    }

                } catch (err) {
                    console.error('Error entering fullscreen:', err);
                    this.showFullscreenError('Failed to enter fullscreen mode');
                }
            }

            async exitFullscreen() {
                try {
                    console.log('Exiting fullscreen mode...');

                    if (document.exitFullscreen) {
                        await document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        await document.webkitExitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        await document.mozCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        await document.msExitFullscreen();
                    }

                } catch (err) {
                    console.error('Error exiting fullscreen:', err);
                    this.showFullscreenError('Failed to exit fullscreen mode');
                }
            }

            handleFullscreenChange() {
                const isCurrentlyFullscreen = !!(document.fullscreenElement ||
                                                 document.webkitFullscreenElement ||
                                                 document.mozFullScreenElement ||
                                                 document.msFullscreenElement);

                console.log(`Fullscreen state changed: ${isCurrentlyFullscreen}`);

                if (isCurrentlyFullscreen && !this.isFullscreen) {
                    // Entering fullscreen
                    this.isFullscreen = true;
                    this.applyFullscreenStyles();
                    this.updateFullscreenButton();
                    this.resizeCanvasForFullscreen();
                } else if (!isCurrentlyFullscreen && this.isFullscreen) {
                    // Exiting fullscreen
                    this.isFullscreen = false;
                    this.removeFullscreenStyles();
                    this.updateFullscreenButton();
                    this.restoreCanvasSize();
                }
            }

            applyFullscreenStyles() {
                console.log('Applying fullscreen styles...');

                // Ensure body and html have no spacing
                document.documentElement.style.margin = '0';
                document.documentElement.style.padding = '0';
                document.documentElement.style.width = '100%';
                document.documentElement.style.height = '100%';
                document.documentElement.style.background = '#000';

                document.body.style.margin = '0';
                document.body.style.padding = '0';
                document.body.style.width = '100%';
                document.body.style.height = '100%';
                document.body.style.background = '#000';
                document.body.style.overflow = 'hidden';

                // Hide viewport controls and control panel
                const viewportControlsContainer = document.querySelector('.viewport-controls-container');
                const controlPanel = document.querySelector('.control-panel');

                if (viewportControlsContainer) viewportControlsContainer.style.display = 'none';
                if (controlPanel) controlPanel.style.display = 'none';

                // Make game container fill the screen completely
                const gameContainer = document.querySelector('.game-container');
                if (gameContainer) {
                    gameContainer.style.position = 'absolute';
                    gameContainer.style.top = '0';
                    gameContainer.style.left = '0';
                    gameContainer.style.right = '0';
                    gameContainer.style.bottom = '0';
                    gameContainer.style.width = '100%';
                    gameContainer.style.height = '100%';
                    gameContainer.style.margin = '0';
                    gameContainer.style.padding = '0';
                    gameContainer.style.zIndex = '9999';
                    gameContainer.style.background = '#000';
                    gameContainer.style.display = 'block';
                }

                // Make game viewport fill the container completely
                if (this.gameViewport) {
                    this.gameViewport.style.position = 'absolute';
                    this.gameViewport.style.top = '0';
                    this.gameViewport.style.left = '0';
                    this.gameViewport.style.right = '0';
                    this.gameViewport.style.bottom = '0';
                    this.gameViewport.style.width = '100%';
                    this.gameViewport.style.height = '100%';
                    this.gameViewport.style.margin = '0';
                    this.gameViewport.style.padding = '0';
                    this.gameViewport.style.maxWidth = 'none';
                    this.gameViewport.style.maxHeight = 'none';
                    this.gameViewport.style.border = 'none';
                    this.gameViewport.style.borderRadius = '0';
                    this.gameViewport.style.background = '#000';
                }

                // Make canvas fill the viewport completely
                if (this.canvas) {
                    this.canvas.style.position = 'absolute';
                    this.canvas.style.top = '0';
                    this.canvas.style.left = '0';
                    this.canvas.style.right = '0';
                    this.canvas.style.bottom = '0';
                    this.canvas.style.width = '100%';
                    this.canvas.style.height = '100%';
                    this.canvas.style.margin = '0';
                    this.canvas.style.padding = '0';
                    this.canvas.style.border = 'none';
                    this.canvas.style.borderRadius = '0';
                }
            }

            removeFullscreenStyles() {
                console.log('Removing fullscreen styles...');

                // Restore body and html styles
                document.documentElement.style.margin = '';
                document.documentElement.style.padding = '';
                document.documentElement.style.width = '';
                document.documentElement.style.height = '';
                document.documentElement.style.background = '';

                document.body.style.margin = '';
                document.body.style.padding = '';
                document.body.style.width = '';
                document.body.style.height = '';
                document.body.style.background = '';
                document.body.style.overflow = '';

                // Show viewport controls and control panel
                const viewportControlsContainer = document.querySelector('.viewport-controls-container');
                const controlPanel = document.querySelector('.control-panel');

                if (viewportControlsContainer) viewportControlsContainer.style.display = '';
                if (controlPanel) controlPanel.style.display = '';

                // Restore game container styles
                const gameContainer = document.querySelector('.game-container');
                if (gameContainer) {
                    gameContainer.style.position = '';
                    gameContainer.style.top = '';
                    gameContainer.style.left = '';
                    gameContainer.style.right = '';
                    gameContainer.style.bottom = '';
                    gameContainer.style.width = '';
                    gameContainer.style.height = '';
                    gameContainer.style.margin = '';
                    gameContainer.style.padding = '';
                    gameContainer.style.zIndex = '';
                    gameContainer.style.background = '';
                    gameContainer.style.display = '';
                }

                // Restore game viewport styles
                if (this.gameViewport) {
                    this.gameViewport.style.position = '';
                    this.gameViewport.style.top = '';
                    this.gameViewport.style.left = '';
                    this.gameViewport.style.right = '';
                    this.gameViewport.style.bottom = '';
                    this.gameViewport.style.width = '';
                    this.gameViewport.style.height = '';
                    this.gameViewport.style.margin = '';
                    this.gameViewport.style.padding = '';
                    this.gameViewport.style.maxWidth = '';
                    this.gameViewport.style.maxHeight = '';
                    this.gameViewport.style.border = '';
                    this.gameViewport.style.borderRadius = '';
                    this.gameViewport.style.background = '';
                }

                // Restore canvas styles
                if (this.canvas) {
                    this.canvas.style.position = '';
                    this.canvas.style.top = '';
                    this.canvas.style.left = '';
                    this.canvas.style.right = '';
                    this.canvas.style.bottom = '';
                    this.canvas.style.width = '';
                    this.canvas.style.height = '';
                    this.canvas.style.margin = '';
                    this.canvas.style.padding = '';
                    this.canvas.style.border = '';
                    this.canvas.style.borderRadius = '';
                }
            }

            updateFullscreenButton() {
                // Update viewport fullscreen button tooltip
                if (this.viewportFullscreenBtn) {
                    this.viewportFullscreenBtn.title = this.isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
                }

                // Update legacy fullscreen button text
                if (this.fullscreenBtn) {
                    this.fullscreenBtn.textContent = this.isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
                }
            }

            resizeCanvasForFullscreen() {
                console.log(`Resizing canvas for fullscreen: ${window.innerWidth}x${window.innerHeight}`);

                // Update CSS variables to use full screen dimensions
                document.documentElement.style.setProperty('--game-width', window.innerWidth + 'px');
                document.documentElement.style.setProperty('--game-height', window.innerHeight + 'px');

                // Trigger canvas resize
                setTimeout(() => {
                    if (window.adjustCanvasDimensions) {
                        window.adjustCanvasDimensions();
                    }
                }, 100);
            }

            restoreCanvasSize() {
                console.log(`Restoring canvas size: ${this.savedViewportSettings.width}x${this.savedViewportSettings.height}`);

                // Restore original CSS variables
                document.documentElement.style.setProperty('--game-width', this.savedViewportSettings.width + 'px');
                document.documentElement.style.setProperty('--game-height', this.savedViewportSettings.height + 'px');

                // Trigger canvas resize
                setTimeout(() => {
                    if (window.adjustCanvasDimensions) {
                        window.adjustCanvasDimensions();
                    }
                }, 100);
            }

            showFullscreenError(message) {
                console.error(message);
                // You could show a toast notification or alert here
                // For now, just log to console
            }

            openSettings() {
                this.settingsPanel.classList.add('show');
                this.overlay.classList.add('show');
            }

            closeSettings() {
                this.settingsPanel.classList.remove('show');
                this.overlay.classList.remove('show');
            }

            updateButtonSelections() {
                // Update visual selection for all option buttons
                this.optionButtons.forEach(button => {
                    const radio = button.querySelector('input[type="radio"]');
                    if (radio && radio.checked) {
                        button.classList.add('selected');
                    } else {
                        button.classList.remove('selected');
                    }
                });

                // Show/hide aspect ratio group based on viewport scaling
                const dynamicScalingSelected = document.querySelector('input[name="viewport-scaling"][value="dynamic"]:checked');
                if (dynamicScalingSelected) {
                    this.aspectRatioGroup.classList.add('show');
                } else {
                    this.aspectRatioGroup.classList.remove('show');
                }
            }

            // Legacy method name for compatibility
            updateResolutionSelection() {
                this.updateButtonSelections();
            }

            updateViewportScaling() {
                const selectedScaling = document.querySelector('input[name="viewport-scaling"]:checked');
                if (selectedScaling) {
                    this.viewportScaling = selectedScaling.value;

                    if (this.viewportScaling === 'dynamic') {
                        this.updateDynamicScaling();
                    } else {
                        // Reset to fixed scaling
                        this.resetFixedScaling();
                    }
                }
            }

            updateDynamicScaling() {
                if (this.viewportScaling !== 'dynamic') return;

                // Calculate available space based on current responsive state
                let availableWidth, availableHeight;

                // Check if we're in responsive mode
                const isResponsive = window.matchMedia('(max-height: 700px), (max-width: 900px)').matches;

                if (isResponsive) {
                    // Responsive: viewport fills entire screen, controls overlay
                    availableWidth = window.innerWidth;
                    availableHeight = window.innerHeight;
                } else {
                    // Desktop: 20px padding + 60px bottom for controls
                    availableWidth = window.innerWidth - 40; // 20px + 20px
                    availableHeight = window.innerHeight - 80; // 20px + 60px
                }

                // Get aspect ratio from selected option
                const selectedAspectRatio = document.querySelector('input[name="aspect-ratio"]:checked');
                const aspectRatioValue = selectedAspectRatio ? selectedAspectRatio.value : '4:3';

                let aspectRatio;
                switch (aspectRatioValue) {
                    case '16:9':
                        aspectRatio = 16 / 9;
                        break;
                    case '16:10':
                        aspectRatio = 16 / 10;
                        break;
                    case '21:9':
                        aspectRatio = 21 / 9;
                        break;
                    case '4:3':
                    default:
                        aspectRatio = 4 / 3;
                        break;
                }

                // Calculate scaled dimensions maintaining aspect ratio
                let scaledWidth = availableWidth;
                let scaledHeight = availableWidth / aspectRatio;

                if (scaledHeight > availableHeight) {
                    scaledHeight = availableHeight;
                    scaledWidth = availableHeight * aspectRatio;
                }

                // Update CSS variables
                document.documentElement.style.setProperty('--game-width', `${scaledWidth}px`);
                document.documentElement.style.setProperty('--game-height', `${scaledHeight}px`);

                // Calculate scale ratio based on a standard base resolution (800x600)
                const baseWidth = 800;
                this.dynamicScaleRatio = scaledWidth / baseWidth;
            }

            resetFixedScaling() {
                // Get selected physical resolution
                const selectedPhysical = document.querySelector('input[name="physical-resolution"]:checked');
                const physicalButton = selectedPhysical ? selectedPhysical.closest('.option-button') : null;

                let width = 800, height = 600;
                if (physicalButton) {
                    width = parseInt(physicalButton.dataset.width);
                    height = parseInt(physicalButton.dataset.height);
                }

                // Reset CSS variables to fixed dimensions
                document.documentElement.style.setProperty('--game-width', `${width}px`);
                document.documentElement.style.setProperty('--game-height', `${height}px`);

                this.dynamicScaleRatio = 1.0;
            }

            applySettings() {
                // Get selected physical resolution
                const selectedPhysicalResolution = document.querySelector('input[name="physical-resolution"]:checked');
                const physicalButton = selectedPhysicalResolution ? selectedPhysicalResolution.closest('.option-button') : null;

                let physicalWidth = 800, physicalHeight = 600;

                if (physicalButton) {
                    physicalWidth = parseInt(physicalButton.dataset.width);
                    physicalHeight = parseInt(physicalButton.dataset.height);
                }

                // Get selected viewport scaling
                const selectedScaling = document.querySelector('input[name="viewport-scaling"]:checked');
                const scalingMode = selectedScaling ? selectedScaling.value : 'fixed';

                const bgColor = document.getElementById('bg-color').value;

                console.log(`Applying physical resolution: ${physicalWidth}x${physicalHeight}`);
                console.log(`Applying viewport scaling: ${scalingMode}`);

                // Update viewport scaling mode
                this.viewportScaling = scalingMode;

                // Update CSS variables based on scaling mode
                if (scalingMode === 'dynamic') {
                    this.updateDynamicScaling();
                } else {
                    // Fixed scaling - use physical resolution
                    document.documentElement.style.setProperty('--game-width', physicalWidth + 'px');
                    document.documentElement.style.setProperty('--game-height', physicalHeight + 'px');
                }

                document.documentElement.style.setProperty('--bg-color', bgColor);

                // Store physical resolution for Godot integration
                this.physicalResolution = { width: physicalWidth, height: physicalHeight };

                // Update global resolution for adjustCanvasDimensions (use physical resolution)
                window.currentRenderResolution = { width: physicalWidth, height: physicalHeight };

                // Update saved viewport settings for fullscreen restoration
                this.savedViewportSettings.width = physicalWidth;
                this.savedViewportSettings.height = physicalHeight;

                this.saveSettings();
                this.closeSettings();

                // Trigger canvas resize and Godot resolution update after a brief delay
                setTimeout(() => {
                    console.log('🔧 Applying settings - updating canvas and Godot resolution');

                    if (window.adjustCanvasDimensions) {
                        window.adjustCanvasDimensions();
                    }

                    // For responsive mode, ensure Godot gets the current viewport size
                    if (scalingMode === 'dynamic') {
                        const currentWidth = window.innerWidth;
                        const currentHeight = window.innerHeight;
                        console.log(`🎯 Responsive mode: updating Godot to viewport size ${currentWidth}x${currentHeight}`);
                        if (window.updateGodotResolution) {
                            window.updateGodotResolution(currentWidth, currentHeight);
                        }
                    } else {
                        // Fixed mode: use the physical resolution
                        console.log(`🎯 Fixed mode: updating Godot to physical resolution ${physicalWidth}x${physicalHeight}`);
                        if (window.updateGodotResolution) {
                            window.updateGodotResolution(physicalWidth, physicalHeight);
                        }
                    }
                }, 100);
            }

            resetSettings() {
                // Reset to SVGA (800x600) - the default physical resolution
                const physicalSvgaRadio = document.getElementById('physical-svga');
                const fixedScalingRadio = document.getElementById('scaling-fixed');
                const aspect43Radio = document.getElementById('aspect-4-3');

                if (physicalSvgaRadio) {
                    physicalSvgaRadio.checked = true;
                }

                if (fixedScalingRadio) {
                    fixedScalingRadio.checked = true;
                    this.viewportScaling = 'fixed';
                    this.resetFixedScaling();
                }

                if (aspect43Radio) {
                    aspect43Radio.checked = true;
                    this.aspectRatio = '4:3';
                }

                this.updateResolutionSelection();
                document.getElementById('bg-color').value = '#2a2a2a';
            }

            saveSettings() {
                const selectedPhysicalResolution = document.querySelector('input[name="physical-resolution"]:checked');
                const selectedViewportScaling = document.querySelector('input[name="viewport-scaling"]:checked');
                const selectedAspectRatio = document.querySelector('input[name="aspect-ratio"]:checked');

                const physicalResolutionValue = selectedPhysicalResolution ? selectedPhysicalResolution.value : '800x600';
                const viewportScalingValue = selectedViewportScaling ? selectedViewportScaling.value : 'fixed';
                const aspectRatioValue = selectedAspectRatio ? selectedAspectRatio.value : '4:3';

                const settings = {
                    physicalResolution: physicalResolutionValue,
                    viewportScaling: viewportScalingValue,
                    aspectRatio: aspectRatioValue,
                    bgColor: document.getElementById('bg-color').value
                };
                localStorage.setItem('gameShellSettings', JSON.stringify(settings));
                console.log('Settings saved:', settings);
            }

            loadSettings() {
                const saved = localStorage.getItem('gameShellSettings');
                if (saved) {
                    try {
                        const settings = JSON.parse(saved);
                        console.log('Loading settings:', settings);

                        // Handle legacy settings - use resolution or renderResolution for physical resolution
                        let physicalResolutionValue = settings.physicalResolution;
                        if (!physicalResolutionValue && settings.renderResolution) {
                            physicalResolutionValue = settings.renderResolution;
                        }
                        if (!physicalResolutionValue && settings.resolution) {
                            physicalResolutionValue = settings.resolution;
                        }

                        // Set physical resolution
                        const physicalResolution = physicalResolutionValue || '800x600';
                        const physicalResolutionRadio = document.querySelector(`input[name="physical-resolution"][value="${physicalResolution}"]`);
                        if (physicalResolutionRadio) {
                            physicalResolutionRadio.checked = true;

                            // Apply physical resolution to CSS variables (viewport size)
                            const option = physicalResolutionRadio.closest('.resolution-option');
                            if (option) {
                                const width = option.dataset.width;
                                const height = option.dataset.height;
                                document.documentElement.style.setProperty('--game-width', width + 'px');
                                document.documentElement.style.setProperty('--game-height', height + 'px');

                                // Update saved viewport settings
                                this.savedViewportSettings.width = parseInt(width);
                                this.savedViewportSettings.height = parseInt(height);

                                // Store physical resolution for Godot integration
                                this.physicalResolution = {
                                    width: parseInt(width),
                                    height: parseInt(height)
                                };
                                // Update global resolution (use physical resolution)
                                window.currentRenderResolution = {
                                    width: parseInt(width),
                                    height: parseInt(height)
                                };
                            }
                        }

                        // Set viewport scaling
                        const viewportScaling = settings.viewportScaling || 'fixed';
                        const viewportScalingRadio = document.querySelector(`input[name="viewport-scaling"][value="${viewportScaling}"]`);
                        if (viewportScalingRadio) {
                            viewportScalingRadio.checked = true;
                            this.viewportScaling = viewportScaling;

                            // Apply scaling mode
                            if (viewportScaling === 'dynamic') {
                                this.updateDynamicScaling();
                            }
                        }

                        // Set aspect ratio
                        const aspectRatio = settings.aspectRatio || '4:3';
                        const aspectRatioRadio = document.querySelector(`input[name="aspect-ratio"][value="${aspectRatio}"]`);
                        if (aspectRatioRadio) {
                            aspectRatioRadio.checked = true;
                            this.aspectRatio = aspectRatio;
                        }

                        this.updateResolutionSelection();

                        // Set background color
                        document.getElementById('bg-color').value = settings.bgColor || '#2a2a2a';
                        document.documentElement.style.setProperty('--bg-color', settings.bgColor || '#2a2a2a');
                    } catch (e) {
                        console.warn('Failed to load saved settings:', e);
                        // Reset to defaults if settings are corrupted
                        localStorage.removeItem('gameShellSettings');
                        this.resetSettings();
                    }
                } else {
                    // No saved settings, ensure default selection is visible
                    this.updateResolutionSelection();
                }
            }
        }

        (function() {
            // Auto-detect the basename from the script tag or use fallback
            let basename = 'stiletto'; // Default fallback

            // Try to detect from script tags
            const scripts = document.querySelectorAll('script[src]');
            for (let script of scripts) {
                const src = script.getAttribute('src');
                if (src && src.endsWith('.js') && !src.includes('worklet')) {
                    basename = src.replace('.js', '');
                    break;
                }
            }

            const EXECUTABLE_NAME = basename;
            const MAIN_PACK = basename + '.pck';
            const INDETERMINATE_STATUS_STEP_MS = 100;

            var canvas = document.getElementById('canvas');
            var loadingScreen = document.getElementById('loading-screen');
            var customProgressBar = document.getElementById('custom-progress-bar');
            var customProgressIndeterminate = document.getElementById('custom-progress-indeterminate');
            var loadingDataCounter = document.getElementById('loading-data-counter');

            console.log('Initializing Godot web shell...');
            console.log('Canvas element:', canvas);
            console.log('Game viewport:', document.querySelector('.game-viewport'));
            console.log('Engine object:', engine);
            console.log('Executable name:', EXECUTABLE_NAME);

            // Initialize canvas dimensions before engine starts
            // Get the actual viewport container dimensions for initial setup
            const gameViewport = document.querySelector('.game-viewport');
            let initialWidth, initialHeight;

            if (gameViewport) {
                const rect = gameViewport.getBoundingClientRect();
                initialWidth = Math.floor(rect.width);
                initialHeight = Math.floor(rect.height);
            } else {
                // Fallback to window dimensions
                initialWidth = window.innerWidth;
                initialHeight = window.innerHeight;
            }

            canvas.width = initialWidth;
            canvas.height = initialHeight;
            canvas.style.width = initialWidth + "px";
            canvas.style.height = initialHeight + "px";
            console.log(`🎯 Pre-initialized canvas: internal=${initialWidth}x${initialHeight}, display=${initialWidth}x${initialHeight}`);

            // Initialize global resolution (default to physical resolution)
            window.currentRenderResolution = { width: 800, height: 600 };
            console.log('Main pack:', MAIN_PACK);

            var initializing = true;
            var statusMode = 'hidden';
            var lastWidth = 0;
            var lastHeight = 0;
            var lastScale = 0;

            var animationCallbacks = [];
            function animate(time) {
                animationCallbacks.forEach(callback => callback(time));
                requestAnimationFrame(animate);
            }
            requestAnimationFrame(animate);

            function adjustCanvasDimensions() {
                if (!canvas) {
                    console.warn('Canvas not found, skipping dimension adjustment');
                    return;
                }

                // Check if we're in fullscreen mode
                const isFullscreen = !!(document.fullscreenElement ||
                                       document.webkitFullscreenElement ||
                                       document.mozFullScreenElement ||
                                       document.msFullscreenElement);

                let width, height;

                if (isFullscreen) {
                    // In fullscreen mode, use the full screen dimensions
                    width = window.innerWidth;
                    height = window.innerHeight;
                    console.log(`Fullscreen mode: using screen dimensions: ${width}x${height}`);
                } else {
                    // In windowed mode, use the actual viewport container dimensions
                    const gameViewport = document.querySelector('.game-viewport');
                    if (gameViewport) {
                        const rect = gameViewport.getBoundingClientRect();
                        width = Math.floor(rect.width);
                        height = Math.floor(rect.height);
                        console.log(`Windowed mode: using viewport container dimensions: ${width}x${height}`);
                    } else {
                        // Fallback to window dimensions if viewport not found
                        width = window.innerWidth;
                        height = window.innerHeight;
                        console.log(`Windowed mode fallback: using window dimensions: ${width}x${height}`);
                    }
                }

                if (lastWidth != width || lastHeight != height) {
                    lastWidth = width;
                    lastHeight = height;

                    // Set canvas internal dimensions to match the viewport exactly
                    canvas.width = width;
                    canvas.height = height;

                    // Set canvas display size (CSS pixels) to fill the viewport
                    canvas.style.width = width + "px";
                    canvas.style.height = height + "px";

                    console.log(`📐 Canvas updated: internal=${width}x${height}, display=${width}x${height}`);

                    // Send resolution update to Godot's ResolutionManager
                    updateGodotResolution(width, height);
                }
            }
            
            // Function to update Godot's internal resolution via ResolutionManager
            function updateGodotResolution(width, height) {
                if (typeof Module !== 'undefined' && Module.ccall) {
                    try {
                        // Call Godot's ResolutionManager.update_viewport_resolution() method
                        const result = Module.ccall('godot_js_eval', 'number', ['string'], [
                            `ResolutionManager.update_viewport_resolution(${width}, ${height}); 1;`
                        ]);

                        if (result === 1) {
                            console.log(`🎯 Successfully updated Godot internal resolution: ${width}x${height}`);
                        } else {
                            console.log(`⚠️ Godot resolution update returned: ${result}`);
                        }
                    } catch (e) {
                        console.log('❌ Failed to update Godot resolution:', e);

                        // Fallback: try the standard canvas resize API
                        try {
                            Module.ccall('godot_js_display_canvas_resize', null, ['number', 'number'], [width, height]);
                            console.log(`🔄 Fallback: Used standard canvas resize API`);
                        } catch (e2) {
                            console.log('❌ Fallback also failed:', e2);
                        }
                    }
                } else {
                    console.log('❌ Module.ccall not available for Godot resolution update');
                }
            }

            // Make functions globally accessible
            window.adjustCanvasDimensions = adjustCanvasDimensions;
            window.updateGodotResolution = updateGodotResolution;

            // Add window resize handler
            window.addEventListener('resize', () => {
                // Update screen resolution detection on resize
                if (typeof gameShell !== 'undefined' && gameShell.detectScreenResolution) {
                    gameShell.detectScreenResolution();
                }
                setTimeout(adjustCanvasDimensions, 50);
            });

            // Initial canvas dimension setup
            adjustCanvasDimensions();

            setStatusMode = (mode) => {
                console.log('Setting loading mode:', mode, 'current:', statusMode, 'initializing:', initializing);

                if (statusMode === mode || !initializing)
                    return;

                switch (mode) {
                    case 'progress':
                        loadingScreen.classList.add('visible');
                        // Show progress bar, hide indeterminate animation
                        customProgressBar.style.display = 'block';
                        customProgressIndeterminate.style.display = 'none';
                        customProgressBar.style.width = '0%';
                        // Reset progress tracking variables
                        maxProgressSeen = 0;
                        progressStartTime = Date.now();
                        currentDataLoaded = 0;
                        unknownTotalCallCount = 0;
                        hasEnteredIndeterminateMode = false;
                        loadingDataCounter.textContent = '';
                        break;
                    case 'indeterminate':
                        loadingScreen.classList.add('visible');
                        // Hide progress bar, show indeterminate animation
                        customProgressBar.style.display = 'none';
                        customProgressIndeterminate.style.display = 'block';
                        // Keep showing data counter with current data
                        if (currentDataLoaded > 0) {
                            loadingDataCounter.textContent = formatDataSize(currentDataLoaded);
                        }
                        break;
                    case 'notice':
                        loadingScreen.classList.add('visible');
                        customProgressBar.style.display = 'none';
                        customProgressIndeterminate.style.display = 'none';
                        break;
                    case 'hidden':
                        loadingScreen.classList.remove('visible');
                        // Reset everything when hiding
                        customProgressBar.style.width = '0%';
                        customProgressBar.style.display = 'block';
                        customProgressIndeterminate.style.display = 'none';
                        loadingDataCounter.textContent = '';
                        currentDataLoaded = 0;
                        break;
                    default:
                        throw new Error('Invalid loading mode');
                }

                statusMode = mode;
            };

            setStatusNotice = (text) => {
                console.log('Loading notice:', text);
                setStatusMode('notice');
            };

            // Custom progress update function
            let maxProgressSeen = 0;
            let progressStartTime = Date.now();
            let currentDataLoaded = 0;

            // Format bytes to MB or GB
            function formatDataSize(bytes) {
                if (bytes === 0) return '';

                const mb = bytes / (1024 * 1024);
                if (mb < 1000) {
                    return mb.toFixed(1) + ' MB';
                } else {
                    const gb = mb / 1024;
                    return gb.toFixed(2) + ' GB';
                }
            }

            let unknownTotalCallCount = 0;
            let hasEnteredIndeterminateMode = false;

            function updateProgress(current, total) {
                console.log('Progress update:', current, '/', total);

                if (current >= 0) {
                    // Update current data loaded and data counter
                    currentDataLoaded = current;
                    loadingDataCounter.textContent = formatDataSize(current);

                    if (total > 0) {
                        // Normal case: we know the total size - use progress mode
                        if (!hasEnteredIndeterminateMode) {
                            const percentage = Math.min(100, Math.max(0, Math.round((current / total) * 100)));
                            console.log('Calculated percentage from total:', percentage + '%');

                            // Ensure progress only moves forward
                            const finalPercentage = Math.max(percentage, maxProgressSeen);
                            maxProgressSeen = finalPercentage;

                            customProgressBar.style.width = finalPercentage + '%';
                            console.log('Setting progress bar to:', finalPercentage + '%');

                            // Check for completion
                            if (current >= total) {
                                console.log('Loading complete (reached total), switching to indeterminate mode');
                                hasEnteredIndeterminateMode = true;
                                setTimeout(() => {
                                    setStatusMode('indeterminate');
                                }, 500);
                            }
                        } else {
                            console.log('Already in indeterminate mode, updating data counter only');
                        }
                    } else {
                        // Total is 0 or unknown
                        if (!hasEnteredIndeterminateMode) {
                            unknownTotalCallCount++;
                            console.log('Unknown total call count:', unknownTotalCallCount, 'bytes:', current);

                            if (unknownTotalCallCount >= 3) {
                                // After 3 calls with unknown total, switch to indeterminate mode
                                console.log('Multiple unknown total calls detected, switching to indeterminate mode');
                                hasEnteredIndeterminateMode = true;
                                setStatusMode('indeterminate');
                            } else {
                                // For the first few calls, show some estimated progress
                                const timeElapsed = (Date.now() - progressStartTime) / 1000;
                                const estimatedTotal = Math.max(current * 2, 50000000); // Estimate 50MB minimum
                                const percentage = Math.min(30, Math.round((current / estimatedTotal) * 100));

                                const finalPercentage = Math.max(percentage, maxProgressSeen);
                                maxProgressSeen = finalPercentage;

                                customProgressBar.style.width = finalPercentage + '%';
                                console.log('Estimated progress (total unknown):', finalPercentage + '%, bytes:', current);
                            }
                        } else {
                            console.log('Already in indeterminate mode, updating data counter only');
                        }
                    }
                } else {
                    console.log('Invalid current value:', current);
                }
            }

            // Progress handling is now done in the engine config above

            function displayFailureNotice(err) {
                var msg = err.message || err;
                console.error(msg);
                setStatusNotice(msg);
                setStatusMode('notice');
                initializing = false;
            };

            // Initialize the game shell first
            const gameShell = new GameShell();

            if (!Engine.isWebGLAvailable()) {
                displayFailureNotice('WebGL not available');
            } else {
                console.log('Starting Godot engine...');
                setStatusMode('indeterminate');

                // Wait for DOM to be fully ready and viewport to have final dimensions
                setTimeout(() => {
                    console.log('Pre-initialization canvas setup...');

                    // Ensure canvas dimensions are set before starting the engine
                    adjustCanvasDimensions();

                    // Log initial viewport state for debugging
                    const gameViewport = document.querySelector('.game-viewport');
                    if (gameViewport) {
                        const rect = gameViewport.getBoundingClientRect();
                        console.log(`Initial viewport dimensions: ${rect.width}x${rect.height}`);
                        console.log(`Window dimensions: ${window.innerWidth}x${window.innerHeight}`);
                        console.log(`Responsive mode: ${window.innerWidth <= 900 || window.innerHeight <= 700}`);
                    }
                }, 50);

                // Use the modern engine.startGame() method with proper progress handling
                const config = {
                    canvas: canvas,
                    executable: EXECUTABLE_NAME,
                    mainPack: MAIN_PACK,
                    canvasResizePolicy: 0, // 0 = None (manual control for responsive behavior)
                    onProgress: function(current, total) {
                        console.log('Engine progress callback:', current, '/', total);
                        // Only set progress mode if we haven't entered indeterminate mode yet
                        if (!hasEnteredIndeterminateMode) {
                            setStatusMode('progress');
                        }
                        updateProgress(current, total);
                    }
                };

                console.log('Starting engine with config:', config);
                const startPromise = engine.startGame(config);

                startPromise.then(() => {
                    console.log('🚀 Godot engine started successfully');
                    console.log('📊 Current canvas state:', {
                        width: canvas.width,
                        height: canvas.height,
                        styleWidth: canvas.style.width,
                        styleHeight: canvas.style.height,
                        windowSize: `${window.innerWidth}x${window.innerHeight}`
                    });

                    setStatusMode('hidden');
                    initializing = false;

                    // Ensure canvas dimensions are set correctly after engine starts
                    console.log('🔧 Engine started, adjusting canvas dimensions...');

                    // Immediate adjustment
                    adjustCanvasDimensions();

                    // Progressive resolution synchronization with increasing delays
                    // This ensures Godot's engine is fully ready before we update its internal resolution
                    setTimeout(() => {
                        console.log('🔧 Post-startup resolution sync (100ms)');
                        adjustCanvasDimensions();
                    }, 100);

                    setTimeout(() => {
                        console.log('🔧 Mid-startup resolution sync (500ms)');
                        adjustCanvasDimensions();
                    }, 500);

                    setTimeout(() => {
                        console.log('🔧 Final resolution sync (1000ms)');
                        adjustCanvasDimensions();
                    }, 1000);

                    // Extended sync for complex scenes
                    setTimeout(() => {
                        console.log('🔧 Extended resolution sync (2000ms)');
                        adjustCanvasDimensions();
                    }, 2000);

                }).catch(displayFailureNotice);
            }
        })();
    //]]></script>
</body>
</html>
