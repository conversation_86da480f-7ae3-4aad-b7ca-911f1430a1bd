@echo off
echo ========================================
echo  Clean Export - Force Fresh Build
echo ========================================
echo.
echo This will:
echo 1. Delete existing web export files
echo 2. Clear Godot's export cache
echo 3. Re-export with compatible settings
echo.
echo Make sure God<PERSON> is closed before running this.
echo.
pause

cd /d "%~dp0..\.."

echo Step 1: Cleaning existing export files...
echo.

REM Delete existing web export files
del "build\test\stiletto.js" 2>nul
del "build\test\stiletto.wasm" 2>nul
del "build\test\stiletto.side.wasm" 2>nul
del "build\test\stiletto.pck" 2>nul
del "build\test\stiletto.audio.worklet.js" 2>nul
del "build\test\stiletto.audio.position.worklet.js" 2>nul

echo Deleted old export files.

echo.
echo Step 2: Clearing Godot export cache...
echo.

REM Clear Godot's export cache (if it exists)
if exist ".godot\export_cache" (
    rmdir /s /q ".godot\export_cache"
    echo Cleared export cache.
) else (
    echo No export cache found.
)

echo.
echo Step 3: Re-exporting project with compatible settings...
echo.

REM Export the project using Godot command line (RELEASE mode for web compatibility)
"D:\Steam\steamapps\common\Godot Engine\godot.windows.opt.tools.64.exe" --headless --export-release "Web" "build/test/stiletto.html"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo  Clean export completed successfully!
    echo ========================================
    echo.
    echo Files exported to: build/test/
    echo.
    echo Next steps:
    echo 1. Clear your browser cache (Ctrl+Shift+Delete)
    echo 2. Test with: build/test/minimal_engine_test.html
    echo 3. If working, try: build/test/stiletto.html
    echo.
    echo For remote testing:
    echo   build/test/start_remote_server.bat
    echo.
) else (
    echo.
    echo ========================================
    echo  Clean export failed!
    echo ========================================
    echo.
    echo Make sure:
    echo 1. Godot is installed at: D:\Steam\steamapps\common\Godot Engine\
    echo 2. The project is not open in Godot editor
    echo 3. You have the Web export template installed
    echo 4. The export preset "Web" exists in your project
    echo.
    echo Try running: build/test/install_web_template.bat
    echo.
)

pause
