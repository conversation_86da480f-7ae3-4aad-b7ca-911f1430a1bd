@echo off
echo ========================================
echo  Export Release Build for Web
echo ========================================
echo.
echo This will create a RELEASE build with:
echo - No debug symbols or source maps
echo - No threading support
echo - No extensions support
echo - Optimized for web compatibility
echo.
echo Make sure Godot is closed before running this.
echo.
pause

cd /d "%~dp0..\.."

echo Exporting RELEASE build...
echo.

REM Export the project using Godot command line (RELEASE mode)
"D:\Steam\steamapps\common\Godot Engine\godot.windows.opt.tools.64.exe" --headless --export-release "Web" "build/test/stiletto.html"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo  Release export completed successfully!
    echo ========================================
    echo.
    echo Files exported to: build/test/
    echo.
    echo IMPORTANT: Clear your browser cache before testing!
    echo.
    echo Test locally:
    echo   http://localhost:8000/stiletto.html
    echo.
    echo Test remotely:
    echo   1. Run: build/test/start_remote_server.bat
    echo   2. Use IP address shown in server output
    echo.
    echo If issues persist, try:
    echo   http://localhost:8000/minimal_engine_test.html
    echo.
) else (
    echo.
    echo ========================================
    echo  Release export failed!
    echo ========================================
    echo.
    echo Make sure:
    echo 1. Godot is installed at: D:\Steam\steamapps\common\Godot Engine\
    echo 2. The project is not open in Godot editor
    echo 3. You have the Web export template installed
    echo 4. The export preset "Web" exists in your project
    echo.
    echo Try running: build/test/install_web_template.bat
    echo.
)

pause
